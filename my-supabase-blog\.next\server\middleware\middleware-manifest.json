{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_f58e8655._.js", "server/edge/chunks/[root-of-the-server]__c6c91fdf._.js", "server/edge/chunks/edge-wrapper_c6630286.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "InWAx9TKRw0SwjwgHaN9o9iJ2f59fSsKSswH0rIg4hk=", "__NEXT_PREVIEW_MODE_ID": "73335f5d730c245ec14cafa719c537a0", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "3660fb246796995f7e1ec345028cf436814fb5a7d1a4b0eda4f8508119cbc5f3", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "360cdb0c21bf2d2a82144f664b95f8b4fdc796bcfa24eaeb5ed6908a6a126200"}}}, "instrumentation": null, "functions": {}}