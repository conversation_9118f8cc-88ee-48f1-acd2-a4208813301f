// import { redirect } from 'next/navigation'
// import { getCurrentUser, isAdmin } from '@/lib/auth'
import Link from 'next/link'
import NewPostForm from './new-post-form'

export default async function NewPostPage() {
  // Temporarily bypass authentication for testing
  // TODO: Re-enable authentication in production
  // const user = await getCurrentUser()

  // For testing purposes, allow access without authentication
  // if (!user) {
  //   redirect('/login')
  // }

  // const userIsAdmin = await isAdmin(user.id)

  // if (!userIsAdmin) {
  //   redirect('/')
  // }

  return (
    <div className="min-h-screen bg-background">
      <div className="max-w-7xl mx-auto px-3 sm:px-4 lg:px-6 xl:px-8 py-4 sm:py-6 lg:py-8">
        <div className="mb-6 sm:mb-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-4">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center mr-4">
                <svg className="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                </svg>
              </div>
              <div>
                <h1 className="text-2xl sm:text-3xl font-bold text-foreground mb-2">Create New Post</h1>
                <p className="text-muted-foreground">Share your thoughts with the world using our markdown editor</p>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="flex items-center gap-3">
              <Link
                href="/admin/manage-posts"
                className="text-muted-foreground hover:text-foreground transition-colors font-medium inline-flex items-center px-3 py-2 rounded-lg hover:bg-muted/50"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
                <span className="hidden sm:inline">Manage Posts</span>
                <span className="sm:hidden">Posts</span>
              </Link>
            </div>
          </div>
        </div>

        <NewPostForm />
      </div>
    </div>
  )
}
