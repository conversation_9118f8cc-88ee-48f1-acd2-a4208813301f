(()=>{var e={};e.id=578,e.ids=[578],e.modules={2507:(e,t,r)=>{"use strict";r.d(t,{U:()=>o});var s=r(9866),n=r(44999);async function o(){let e=await (0,n.UL)();return(0,s.createServerClient)("https://sqoixvgmroejgaebxyeq.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.SFkF9v0Y-lrJuQHiSf5HLTLuwgOXRf0ERQKnfQNfLsU",{cookies:{getAll:()=>e.getAll(),setAll(t){try{t.forEach(({name:t,value:r,options:s})=>e.set(t,r,s))}catch{}}}})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4311:(e,t,r)=>{"use strict";r.d(t,{default:()=>u});var s=r(60687),n=r(43210),o=r(16189),i=r(85814),a=r.n(i),l=r(79481),d=r(62688);let c=(0,d.A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]),m=(0,d.A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),h=(0,d.A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]]),x=(0,d.A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);function u({posts:e}){let[t,r]=(0,n.useState)(e),[i,d]=(0,n.useState)(null),[u,p]=(0,n.useState)("latest"),[f,g]=(0,n.useState)(!1),v=(0,o.useRouter)(),b=async(e,s)=>{if(confirm(`Are you sure you want to delete "${s}"? This action cannot be undone.`)){d(e);try{let s=(0,l.U)(),{error:n}=await s.from("posts").delete().eq("id",e);if(n)throw n;r(t.filter(t=>t.id!==e)),v.refresh()}catch(e){console.error("Error deleting post:",e),alert("Error deleting post. Please try again.")}finally{d(null)}}},j=async()=>{g(!0);try{let e=(0,l.U)(),{data:t,error:s}=await e.from("posts").select("*").order("created_at",{ascending:!1});if(s)throw s;r(t||[]),v.refresh()}catch(e){console.error("Error refreshing posts:",e),alert("Error refreshing posts. Please try again.")}finally{g(!1)}},y=[...t].sort((e,t)=>{switch(u){case"latest":return new Date(t.created_at).getTime()-new Date(e.created_at).getTime();case"oldest":return new Date(e.created_at).getTime()-new Date(t.created_at).getTime();case"title-asc":return e.title.localeCompare(t.title);case"title-desc":return t.title.localeCompare(e.title);default:return 0}});return 0===t.length?(0,s.jsx)("div",{className:"text-center py-16 lg:py-24",children:(0,s.jsxs)("div",{className:"max-w-md mx-auto",children:[(0,s.jsx)("div",{className:"w-16 h-16 mx-auto mb-6 bg-muted rounded-full flex items-center justify-center",children:(0,s.jsx)("svg",{className:"w-8 h-8 text-muted-foreground",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})}),(0,s.jsx)("h2",{className:"text-2xl font-semibold text-foreground mb-2",children:"No posts yet"}),(0,s.jsx)("p",{className:"text-muted-foreground mb-6",children:"Start creating content for your blog!"}),(0,s.jsxs)(a(),{href:"/admin/new-post",className:"inline-flex items-center px-6 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-all duration-200 font-medium shadow-sm hover:shadow-md",children:[(0,s.jsx)("svg",{className:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4v16m8-8H4"})}),"Create your first post"]})]})}):(0,s.jsxs)("div",{className:"space-y-6 lg:space-y-8",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 lg:gap-6 mb-8",children:[(0,s.jsx)("div",{className:"bg-card rounded-xl border border-border p-6 hover-lift animate-fade-in shadow-sm hover:shadow-md transition-all duration-200",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"p-3 bg-primary/10 rounded-xl",children:(0,s.jsx)("svg",{className:"h-6 w-6 text-primary",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})}),(0,s.jsxs)("div",{className:"ml-4",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Total Posts"}),(0,s.jsx)("p",{className:"text-2xl lg:text-3xl font-bold text-foreground",children:t.length})]})]})}),(0,s.jsx)("div",{className:"bg-card rounded-xl border border-border p-6 hover-lift animate-fade-in shadow-sm hover:shadow-md transition-all duration-200",style:{animationDelay:"0.1s"},children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"p-3 bg-green-100 dark:bg-green-900/20 rounded-xl",children:(0,s.jsx)("svg",{className:"h-6 w-6 text-green-600 dark:text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,s.jsxs)("div",{className:"ml-4",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Published Today"}),(0,s.jsx)("p",{className:"text-2xl lg:text-3xl font-bold text-foreground",children:t.filter(e=>{let t=new Date;return new Date(e.created_at).toDateString()===t.toDateString()}).length})]})]})}),(0,s.jsx)("div",{className:"bg-card rounded-xl border border-border p-6 hover-lift animate-fade-in shadow-sm hover:shadow-md transition-all duration-200",style:{animationDelay:"0.2s"},children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"p-3 bg-blue-100 dark:bg-blue-900/20 rounded-xl",children:(0,s.jsx)("svg",{className:"h-6 w-6 text-blue-600 dark:text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"})})}),(0,s.jsxs)("div",{className:"ml-4",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"This Week"}),(0,s.jsx)("p",{className:"text-2xl lg:text-3xl font-bold text-foreground",children:t.filter(e=>{let t=new Date;return t.setDate(t.getDate()-7),new Date(e.created_at)>t}).length})]})]})})]}),(0,s.jsxs)("div",{className:"bg-card rounded-2xl border border-border overflow-hidden shadow-xl",children:[(0,s.jsx)("div",{className:"bg-gradient-to-r from-muted/50 to-muted/30 px-6 py-4 border-b border-border",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("h3",{className:"text-lg font-semibold text-foreground flex items-center",children:[(0,s.jsx)("svg",{className:"w-5 h-5 mr-2 text-primary",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),"All Posts (",t.length,")"]}),(0,s.jsxs)("div",{className:"flex items-center gap-2 text-sm text-muted-foreground",children:[(0,s.jsx)("span",{className:"hidden sm:inline",children:"Sort by:"}),(0,s.jsxs)("select",{value:u,onChange:e=>p(e.target.value),className:"bg-background border border-input rounded-lg px-3 py-1 text-sm hover:border-primary/50 focus:border-primary focus:outline-none transition-colors",children:[(0,s.jsx)("option",{value:"latest",children:"Latest"}),(0,s.jsx)("option",{value:"oldest",children:"Oldest"}),(0,s.jsx)("option",{value:"title-asc",children:"Title A-Z"}),(0,s.jsx)("option",{value:"title-desc",children:"Title Z-A"})]})]})]})}),(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"w-full",children:[(0,s.jsx)("thead",{className:"bg-muted/30",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"px-3 py-4 text-center text-xs font-semibold text-muted-foreground uppercase tracking-wider w-12",children:(0,s.jsx)("input",{type:"checkbox",className:"rounded border-border text-primary focus:ring-primary",onChange:e=>{document.querySelectorAll("input[data-post-id]").forEach(t=>{t.checked=e.target.checked})}})}),(0,s.jsx)("th",{className:"px-6 py-4 text-left text-xs font-semibold text-muted-foreground uppercase tracking-wider",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a.997.997 0 01-1.414 0l-7-7A1.997 1.997 0 013 12V7a4 4 0 014-4z"})}),"Title"]})}),(0,s.jsx)("th",{className:"px-6 py-4 text-left text-xs font-semibold text-muted-foreground uppercase tracking-wider hidden sm:table-cell",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})}),"Published"]})}),(0,s.jsx)("th",{className:"px-6 py-4 text-left text-xs font-semibold text-muted-foreground uppercase tracking-wider hidden lg:table-cell",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})}),"Updated"]})}),(0,s.jsx)("th",{className:"px-6 py-4 text-center text-xs font-semibold text-muted-foreground uppercase tracking-wider",children:(0,s.jsxs)("div",{className:"flex items-center justify-center",children:[(0,s.jsx)("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"})}),"Actions"]})})]})}),(0,s.jsx)("tbody",{className:"divide-y divide-border/50",children:y.map((e,t)=>(0,s.jsxs)("tr",{className:"hover:bg-gradient-to-r hover:from-muted/20 hover:to-transparent transition-all duration-300 animate-slide-in group",style:{animationDelay:`${.05*t}s`},children:[(0,s.jsx)("td",{className:"px-3 py-6 text-center",children:(0,s.jsx)("input",{type:"checkbox","data-post-id":e.id,className:"rounded border-border text-primary focus:ring-primary"})}),(0,s.jsx)("td",{className:"px-6 py-6",children:(0,s.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,s.jsx)("div",{className:"w-10 h-10 bg-primary/10 rounded-xl flex items-center justify-center flex-shrink-0 group-hover:bg-primary/20 transition-colors",children:(0,s.jsx)("svg",{className:"w-5 h-5 text-primary",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})}),(0,s.jsxs)("div",{className:"min-w-0 flex-1",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,s.jsx)("h4",{className:"text-base font-semibold text-card-foreground truncate group-hover:text-primary transition-colors",children:e.title}),(0,s.jsxs)("span",{className:"text-xs text-muted-foreground bg-muted px-2 py-1 rounded-full",children:[Math.ceil(e.content.split(" ").length/200)," min"]})]}),(0,s.jsxs)("p",{className:"text-sm text-muted-foreground line-clamp-2 leading-relaxed",children:[e.content.substring(0,120),e.content.length>120&&"..."]}),(0,s.jsxs)("div",{className:"flex items-center gap-4 mt-3 text-xs text-muted-foreground",children:[(0,s.jsxs)("span",{className:"flex items-center",children:[(0,s.jsx)("svg",{className:"w-3 h-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})}),e.content.split(" ").length," words"]}),(0,s.jsxs)("span",{className:"sm:hidden flex items-center",children:[(0,s.jsx)(c,{className:"w-3 h-3 mr-1"}),new Date(e.created_at).toLocaleDateString("en-US",{month:"short",day:"numeric"})]})]})]})]})}),(0,s.jsx)("td",{className:"px-6 py-6 whitespace-nowrap hidden sm:table-cell",children:(0,s.jsxs)("div",{className:"flex flex-col",children:[(0,s.jsxs)("div",{className:"flex items-center text-sm font-medium text-foreground mb-1",children:[(0,s.jsx)(c,{className:"w-4 h-4 mr-2 text-primary"}),new Date(e.created_at).toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"})]}),(0,s.jsx)("div",{className:"text-xs text-muted-foreground",children:new Date(e.created_at).toLocaleTimeString("en-US",{hour:"2-digit",minute:"2-digit"})})]})}),(0,s.jsx)("td",{className:"px-6 py-6 whitespace-nowrap hidden lg:table-cell",children:(0,s.jsxs)("div",{className:"flex flex-col",children:[(0,s.jsx)("div",{className:"text-sm font-medium text-foreground mb-1",children:new Date(e.updated_at).toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"})}),(0,s.jsx)("div",{className:"text-xs text-muted-foreground",children:e.updated_at!==e.created_at?(0,s.jsxs)("span",{className:"flex items-center",children:[(0,s.jsx)("svg",{className:"w-3 h-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})}),"Modified"]}):"No changes"})]})}),(0,s.jsx)("td",{className:"px-6 py-6 whitespace-nowrap",children:(0,s.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,s.jsx)(a(),{href:`/posts/${e.id}`,className:"inline-flex items-center justify-center w-9 h-9 text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-all duration-200 rounded-xl hover:bg-blue-50 dark:hover:bg-blue-900/20 group/action",title:"View post",children:(0,s.jsx)(m,{className:"w-4 h-4 group-hover/action:scale-110 transition-transform"})}),(0,s.jsx)(a(),{href:`/admin/edit-post/${e.id}`,className:"inline-flex items-center justify-center w-9 h-9 text-green-600 dark:text-green-400 hover:text-green-700 dark:hover:text-green-300 transition-all duration-200 rounded-xl hover:bg-green-50 dark:hover:bg-green-900/20 group/action",title:"Edit post",children:(0,s.jsx)(h,{className:"w-4 h-4 group-hover/action:scale-110 transition-transform"})}),(0,s.jsx)("button",{onClick:()=>b(e.id,e.title),disabled:i===e.id,className:"inline-flex items-center justify-center w-9 h-9 text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 transition-all duration-200 rounded-xl hover:bg-red-50 dark:hover:bg-red-900/20 disabled:opacity-50 disabled:cursor-not-allowed group/action",title:"Delete post",children:i===e.id?(0,s.jsx)("div",{className:"w-4 h-4 animate-spin rounded-full border-2 border-current border-t-transparent"}):(0,s.jsx)(x,{className:"w-4 h-4 group-hover/action:scale-110 transition-transform"})})]})})]},e.id))})]})}),(0,s.jsx)("div",{className:"bg-muted/20 px-6 py-4 border-t border-border",children:(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,s.jsxs)("div",{className:"flex items-center text-sm text-muted-foreground",children:[(0,s.jsx)("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})}),"Showing ",t.length," ",1===t.length?"post":"posts"]}),(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsxs)("button",{onClick:()=>{let e="data:application/json;charset=utf-8,"+encodeURIComponent(JSON.stringify(t,null,2)),r=`blog-posts-${new Date().toISOString().split("T")[0]}.json`,s=document.createElement("a");s.setAttribute("href",e),s.setAttribute("download",r),s.click()},className:"inline-flex items-center px-3 py-2 text-sm font-medium text-muted-foreground bg-background border border-border rounded-lg hover:bg-muted hover:text-foreground transition-colors",children:[(0,s.jsx)("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4-4m0 0l-4 4m4-4v12"})}),"Export"]}),(0,s.jsxs)("button",{onClick:j,disabled:f,className:"inline-flex items-center px-3 py-2 text-sm font-medium text-muted-foreground bg-background border border-border rounded-lg hover:bg-muted hover:text-foreground transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,s.jsx)("svg",{className:`w-4 h-4 mr-2 ${f?"animate-spin":""}`,fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),f?"Refreshing...":"Refresh"]})]})]})})]}),(0,s.jsxs)("div",{className:"mt-8 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,s.jsxs)("div",{className:"bg-card rounded-xl border border-border p-6 hover-lift",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsx)("h4",{className:"font-semibold text-foreground",children:"Quick Actions"}),(0,s.jsx)("svg",{className:"w-5 h-5 text-primary",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 10V3L4 14h7v7l9-11h-7z"})})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)(a(),{href:"/admin/new-post",className:"flex items-center text-sm text-muted-foreground hover:text-primary transition-colors",children:[(0,s.jsx)("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4v16m8-8H4"})}),"Create new post"]}),(0,s.jsxs)("button",{onClick:()=>{let e=document.createElement("input");e.type="file",e.accept=".json",e.onchange=async e=>{let t=e.target.files?.[0];if(t)try{let e=await t.text(),r=JSON.parse(e);if(!Array.isArray(r))return void alert("Invalid file format. Please select a valid JSON file with posts array.");let s=(0,l.U)(),{error:n}=await s.from("posts").insert(r.map(e=>({title:e.title,content:e.content,created_at:e.created_at||new Date().toISOString(),updated_at:new Date().toISOString()})));if(n)throw n;alert(`Successfully imported ${r.length} posts!`),j()}catch(e){console.error("Error importing posts:",e),alert("Error importing posts. Please check the file format and try again.")}},e.click()},className:"flex items-center text-sm text-muted-foreground hover:text-primary transition-colors",children:[(0,s.jsx)("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"})}),"Import posts"]}),(0,s.jsxs)("button",{onClick:()=>{let e=t.filter((e,r)=>{let s=document.querySelector(`input[data-post-id="${t[r].id}"]`);return s?.checked});if(0===e.length)return void alert("Please select posts to perform bulk actions.");let r=prompt(`Selected ${e.length} posts. Choose action:
1. Delete
2. Export

Enter 1 or 2:`);if("1"===r)confirm(`Are you sure you want to delete ${e.length} selected posts? This action cannot be undone.`)&&e.forEach(e=>b(e.id,e.title));else if("2"===r){let t="data:application/json;charset=utf-8,"+encodeURIComponent(JSON.stringify(e,null,2)),r=`selected-posts-${new Date().toISOString().split("T")[0]}.json`,s=document.createElement("a");s.setAttribute("href",t),s.setAttribute("download",r),s.click()}},className:"flex items-center text-sm text-muted-foreground hover:text-primary transition-colors",children:[(0,s.jsx)("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"})}),"Bulk actions"]})]})]}),(0,s.jsxs)("div",{className:"bg-card rounded-xl border border-border p-6 hover-lift",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsx)("h4",{className:"font-semibold text-foreground",children:"Recent Activity"}),(0,s.jsx)("svg",{className:"w-5 h-5 text-primary",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})]}),(0,s.jsx)("div",{className:"space-y-3",children:t.slice(0,3).map(e=>(0,s.jsxs)("div",{className:"flex items-center text-sm",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-primary rounded-full mr-3"}),(0,s.jsxs)("span",{className:"text-muted-foreground truncate",children:[e.title.substring(0,30),"..."]})]},e.id))})]}),(0,s.jsxs)("div",{className:"bg-card rounded-xl border border-border p-6 hover-lift",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsx)("h4",{className:"font-semibold text-foreground",children:"Storage"}),(0,s.jsx)("svg",{className:"w-5 h-5 text-primary",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4"})})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,s.jsx)("span",{className:"text-muted-foreground",children:"Posts"}),(0,s.jsx)("span",{className:"font-medium",children:t.length})]}),(0,s.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,s.jsx)("span",{className:"text-muted-foreground",children:"Total words"}),(0,s.jsx)("span",{className:"font-medium",children:t.reduce((e,t)=>e+t.content.split(" ").length,0).toLocaleString()})]}),(0,s.jsx)("div",{className:"w-full bg-muted rounded-full h-2",children:(0,s.jsx)("div",{className:"bg-primary h-2 rounded-full w-3/4"})}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:"75% of storage used"})]})]})]})]})}},8694:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\test2\\\\my-supabase-blog\\\\src\\\\app\\\\admin\\\\manage-posts\\\\manage-posts-client.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\admin\\manage-posts\\manage-posts-client.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},12909:(e,t,r)=>{"use strict";r.d(t,{HW:()=>n,qc:()=>o});var s=r(2507);async function n(){let e=await (0,s.U)(),{data:{user:t}}=await e.auth.getUser();return t}async function o(e){if(!e)return!1;let t=await (0,s.U)(),{data:r}=await t.from("profiles").select("is_admin").eq("id",e).single();if(r?.is_admin)return!0;let{data:{user:n}}=await t.auth.getUser(),o=process.env.ADMIN_EMAIL;return n?.email===o}},18375:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22506:(e,t,r)=>{"use strict";r.d(t,{ThemeToggle:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call ThemeToggle() from the server but ThemeToggle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\theme-toggle.tsx","ThemeToggle")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},35247:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},39727:()=>{},43984:(e,t,r)=>{"use strict";r.d(t,{ThemeToggle:()=>l});var s=r(60687),n=r(43210),o=r(21134),i=r(363),a=r(10218);function l(){let{theme:e,setTheme:t}=(0,a.D)(),[r,l]=n.useState(!1);return(n.useEffect(()=>{l(!0)},[]),r)?(0,s.jsxs)("button",{onClick:()=>t("light"===e?"dark":"light"),className:"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 w-10",children:["light"===e?(0,s.jsx)(i.A,{className:"h-[1.2rem] w-[1.2rem]"}):(0,s.jsx)(o.A,{className:"h-[1.2rem] w-[1.2rem]"}),(0,s.jsx)("span",{className:"sr-only",children:"切換主題"})]}):(0,s.jsxs)("button",{className:"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 w-10",children:[(0,s.jsx)(o.A,{className:"h-[1.2rem] w-[1.2rem]"}),(0,s.jsx)("span",{className:"sr-only",children:"切換主題"})]})}},47990:()=>{},52331:(e,t,r)=>{Promise.resolve().then(r.bind(r,96871))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56798:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>h,tree:()=>d});var s=r(65239),n=r(48088),o=r(88170),i=r.n(o),a=r(30893),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let d={children:["",{children:["admin",{children:["manage-posts",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,63902)),"C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\admin\\manage-posts\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\app\\admin\\manage-posts\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},h=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/admin/manage-posts/page",pathname:"/admin/manage-posts",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63902:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>h});var s=r(37413),n=r(39916),o=r(4536),i=r.n(o),a=r(12909),l=r(2507),d=r(22506),c=r(8694);async function m(){let e=await (0,l.U)(),{data:t,error:r}=await e.from("posts").select("*").order("created_at",{ascending:!1});return r?(console.error("Error fetching posts:",r),[]):t||[]}async function h(){let e=await (0,a.HW)();e||(0,n.redirect)("/login"),await (0,a.qc)(e.id)||(0,n.redirect)("/");let t=await m();return(0,s.jsxs)("div",{className:"min-h-screen bg-background",children:[(0,s.jsx)("header",{className:"bg-card/95 shadow-sm border-b border-border sticky top-0 z-50 backdrop-blur-sm",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 lg:py-6",children:(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)(i(),{href:"/",className:"text-xl sm:text-2xl font-bold text-foreground hover:text-primary transition-colors",children:"My Blog"}),(0,s.jsxs)("div",{className:"flex items-center gap-2 sm:gap-4",children:[(0,s.jsx)(d.ThemeToggle,{}),(0,s.jsxs)(i(),{href:"/admin/new-post",className:"bg-primary text-primary-foreground px-3 sm:px-4 py-2 rounded-md hover:bg-primary/90 transition-all duration-200 text-sm font-medium shadow-sm hover:shadow-md",children:[(0,s.jsx)("span",{className:"hidden sm:inline",children:"New Post"}),(0,s.jsx)("span",{className:"sm:hidden",children:"+"})]}),(0,s.jsx)("form",{action:"/auth/signout",method:"post",children:(0,s.jsxs)("button",{type:"submit",className:"inline-flex items-center justify-center px-3 py-2 rounded-md text-sm font-medium transition-colors bg-destructive text-destructive-foreground hover:bg-destructive/90 shadow-sm hover:shadow-md",children:[(0,s.jsx)("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"})}),"Sign Out"]})})]})]})})}),(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 lg:py-12",children:[(0,s.jsx)("div",{className:"mb-8 lg:mb-12",children:(0,s.jsxs)("div",{className:"flex items-center mb-4",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-primary/10 rounded-xl flex items-center justify-center mr-4",children:(0,s.jsx)("svg",{className:"w-6 h-6 text-primary",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl sm:text-3xl font-bold text-foreground mb-2",children:"Manage Posts"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"View, edit, and delete your blog posts"})]})]})}),(0,s.jsx)(c.default,{posts:t})]})]})}},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(31658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},71829:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,4536,23)),Promise.resolve().then(r.bind(r,8694)),Promise.resolve().then(r.bind(r,22506))},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79481:(e,t,r)=>{"use strict";r.d(t,{U:()=>n});var s=r(59522);function n(){return(0,s.createBrowserClient)("https://sqoixvgmroejgaebxyeq.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.SFkF9v0Y-lrJuQHiSf5HLTLuwgOXRf0ERQKnfQNfLsU")}},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83701:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\test2\\my-supabase-blog\\src\\components\\theme-provider.tsx","ThemeProvider")},85397:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,85814,23)),Promise.resolve().then(r.bind(r,4311)),Promise.resolve().then(r.bind(r,43984))},91645:e=>{"use strict";e.exports=require("net")},92083:(e,t,r)=>{Promise.resolve().then(r.bind(r,83701))},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c,metadata:()=>d});var s=r(37413),n=r(22376),o=r.n(n),i=r(68726),a=r.n(i);r(61135);var l=r(83701);let d={title:"My Blog",description:"A modern blog built with Next.js and Supabase",icons:{icon:"/icon.png"}};function c({children:e}){return(0,s.jsx)("html",{lang:"en",suppressHydrationWarning:!0,children:(0,s.jsx)("body",{className:`${o().variable} ${a().variable} antialiased`,suppressHydrationWarning:!0,children:(0,s.jsx)(l.ThemeProvider,{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!0,suppressHydrationWarning:!0,children:e})})})}},94735:e=>{"use strict";e.exports=require("events")},96871:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>o});var s=r(60687);r(43210);var n=r(10218);function o({children:e,...t}){return(0,s.jsx)(n.N,{...t,children:e})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,98,567,866,318,694],()=>r(56798));module.exports=s})();